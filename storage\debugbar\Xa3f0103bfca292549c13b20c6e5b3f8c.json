{"__meta": {"id": "Xa3f0103bfca292549c13b20c6e5b3f8c", "datetime": "2025-07-29 15:26:00", "utime": **********.923351, "method": "POST", "uri": "/livewire/message/accounting.customers.customer-proposal", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 4, "messages": [{"message": "[15:26:00] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.842514, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:00] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.904217, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:00] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.904259, "xdebug_link": null, "collector": "log"}, {"message": "[15:26:00] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.904295, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.487704, "end": **********.923369, "duration": 0.4356648921966553, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.487704, "relative_start": 0, "end": **********.822681, "relative_end": **********.822681, "duration": 0.3349769115447998, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.82269, "relative_start": 0.*****************, "end": **********.92337, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.accounting.customers.include.proposal (\\resources\\views\\livewire\\accounting\\customers\\include\\proposal.blade.php)", "param_count": 32, "params": ["errors", "_instance", "customerId", "search", "perPage", "sortField", "sortDirection", "showFullView", "proposals", "total", "currentPage", "lastPage", "loading", "error", "editId", "deleteId", "deleteTitle", "page", "paginators", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0039000000000000003, "accumulated_duration_str": "3.9ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00321, "duration_str": "3.21ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 82.308}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 82.308, "width_percent": 17.692}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\User": 1}, "count": 2}, "livewire": {"data": {"accounting.customers.customer-proposal #ixxgty3UC0xUBgTYgWeU": "array:5 [\n  \"data\" => array:17 [\n    \"customerId\" => \"564\"\n    \"search\" => \"\"\n    \"perPage\" => 10\n    \"sortField\" => \"issue_date\"\n    \"sortDirection\" => \"desc\"\n    \"showFullView\" => false\n    \"proposals\" => array:2 [\n      0 => array:6 [\n        \"id\" => 224\n        \"proposal_number\" => \"#PROP000007\"\n        \"issue_date\" => \"2025-07-29\"\n        \"amount\" => 200\n        \"status\" => \"Draft\"\n        \"status_color\" => \"primary\"\n      ]\n      1 => array:6 [\n        \"id\" => 225\n        \"proposal_number\" => \"#PROP000008\"\n        \"issue_date\" => \"2025-07-27\"\n        \"amount\" => 100\n        \"status\" => \"Draft\"\n        \"status_color\" => \"primary\"\n      ]\n    ]\n    \"total\" => 2\n    \"currentPage\" => 1\n    \"lastPage\" => 1\n    \"loading\" => false\n    \"error\" => null\n    \"editId\" => null\n    \"deleteId\" => 225\n    \"deleteTitle\" => \"#PROP000008\"\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"accounting.customers.customer-proposal\"\n  \"view\" => \"livewire.accounting.customers.include.proposal\"\n  \"component\" => \"App\\Http\\Livewire\\Accounting\\Customers\\CustomerProposal\"\n  \"id\" => \"ixxgty3UC0xUBgTYgWeU\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=Xd80f94aac4c22c1e4bce5edd7b0b474a&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/accounting.customers.customer-proposal", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ixxgty3UC0xUBgTYgWeU</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"38 characters\">accounting.customers.customer-proposal</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"223 characters\">finance/customers/view/eyJpdiI6IkRmeEJvMnRHR203aDlJTWE3Y2psYWc9PSIsInZhbHVlIjoibUtBMXBFa2hyaVB6MEhpWGFKSDk1UT09IiwibWFjIjoiZmI0NTIwMTU5ZDdmYTUyOTM2ZWY3ZmNlNTFiNzBjZGIwNzg0ODJmOTVkZTE1Y2Q4NTc5OWQwOWE3NzFhODI0NSIsInRhZyI6IiJ9</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l1699047838-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ZLtPq4jFGNyvLd8uN0mp</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">458a599d</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:17</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>customerId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">564</span>\"\n      \"<span class=sf-dump-key>search</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"10 characters\">issue_date</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n      \"<span class=sf-dump-key>showFullView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>proposals</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>224</span>\n          \"<span class=sf-dump-key>proposal_number</span>\" => \"<span class=sf-dump-str title=\"11 characters\">#PROP000007</span>\"\n          \"<span class=sf-dump-key>issue_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-29</span>\"\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>200</span>\n          \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Draft</span>\"\n          \"<span class=sf-dump-key>status_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">primary</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>225</span>\n          \"<span class=sf-dump-key>proposal_number</span>\" => \"<span class=sf-dump-str title=\"11 characters\">#PROP000008</span>\"\n          \"<span class=sf-dump-key>issue_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-27</span>\"\n          \"<span class=sf-dump-key>amount</span>\" => <span class=sf-dump-num>100</span>\n          \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Draft</span>\"\n          \"<span class=sf-dump-key>status_color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">primary</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>currentPage</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>lastPage</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>loading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>error</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>editId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>deleteId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>deleteTitle</span>\" => \"\"\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">e5a3958988bc2ab57bb79744830f6ce8a812ae443906ab952c9850e50678a597</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">dycq</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">openDeleteModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-num>225</span>\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">#PROP000008</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1448408790 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1223</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IkRmeEJvMnRHR203aDlJTWE3Y2psYWc9PSIsInZhbHVlIjoibUtBMXBFa2hyaVB6MEhpWGFKSDk1UT09IiwibWFjIjoiZmI0NTIwMTU5ZDdmYTUyOTM2ZWY3ZmNlNTFiNzBjZGIwNzg0ODJmOTVkZTE1Y2Q4NTc5OWQwOWE3NzFhODI0NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IncrS3FldEdsaUZvYnE0VTZTN2hRNkE9PSIsInZhbHVlIjoiemtTendXc1kwbTRmQ2NEN3lCLy9pbE9oQVFHcXEydG1yZVlKS21sYkZDTEJsSnFyNFBhdndPUXQyVmlxanN5WWo4NWNtcjZDWml5bXFBbXE0US9BOGoxakYxSmQrTGd5U0tJc1JreVVtVWFuS0xFdDVtRGpDMklJNkpHd1RFb0IiLCJtYWMiOiIyZWI1YmYzNWYwZDhmMzAwNzhhMDc5YmFkMzZkZTdmNTViZDE2YjFhNjJlNzc1MzQ2ZDY5M2ZlN2JhMGUzMDZiIiwidGFnIjoiIn0%3D; osool_session=bDPfqiO28wvTXLx2YaZjOU6qnhAZUsm1kF9z5W2l</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448408790\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1223</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IkRmeEJvMnRHR203aDlJTWE3Y2psYWc9PSIsInZhbHVlIjoibUtBMXBFa2hyaVB6MEhpWGFKSDk1UT09IiwibWFjIjoiZmI0NTIwMTU5ZDdmYTUyOTM2ZWY3ZmNlNTFiNzBjZGIwNzg0ODJmOTVkZTE1Y2Q4NTc5OWQwOWE3NzFhODI0NSIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IncrS3FldEdsaUZvYnE0VTZTN2hRNkE9PSIsInZhbHVlIjoiemtTendXc1kwbTRmQ2NEN3lCLy9pbE9oQVFHcXEydG1yZVlKS21sYkZDTEJsSnFyNFBhdndPUXQyVmlxanN5WWo4NWNtcjZDWml5bXFBbXE0US9BOGoxakYxSmQrTGd5U0tJc1JreVVtVWFuS0xFdDVtRGpDMklJNkpHd1RFb0IiLCJtYWMiOiIyZWI1YmYzNWYwZDhmMzAwNzhhMDc5YmFkMzZkZTdmNTViZDE2YjFhNjJlNzc1MzQ2ZDY5M2ZlN2JhMGUzMDZiIiwidGFnIjoiIn0%3D; osool_session=bDPfqiO28wvTXLx2YaZjOU6qnhAZUsm1kF9z5W2l</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55153</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/livewire/message/accounting.customers.customer-proposal</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/livewire/message/accounting.customers.customer-proposal</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.4877</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-711121792 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 12:26:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9EWTd1Vzgxc3JVT2N4WjBFTnBwcFE9PSIsInZhbHVlIjoiRlNpd2UrZm9sekQ1MmhUc1piMVllRnlSMGVWckhWZkZjSVRJc1hyRzZjejd1Q3B5TkVjN1ZmcFExY0t1WDhCZjY3aEJ0cGZsNFFmaHh3Ujl0dmxtMHgxVWZMM3RjVlE5ZENndkhkeWFTRzRHWFNVTDhmR0pFK1lvQ2xLR3NEdzMiLCJtYWMiOiI3MWVhMTRjZGFmYzc1NTJhN2M4YWI0NjBlN2VhNWY0NWIwYmE0OGViZjhkOTc0MThmMjE2ZWE0MjFiZmNkYTA2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:26:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImhyK2xxODhNUExoZUxtSXVkamNmd3c9PSIsInZhbHVlIjoiVmJUZ2xRZ3hlNlhNcVpjRktFTkFwZHQ1and3aEZ1eU1NY0YvRU82OHhmTURocGozMmVSU2FrVHoxTmxUSytIQmFwc3E3RDRhL1dCTlE0VFN4amZqMXdFR3ZyVW9MVnByS0Q5WGtUYXUxcllTM1FtM1JZcGFjZzBQaVp4cUZsMVEiLCJtYWMiOiIwYjI1MjI3Njk5ODgzYWY4ZmQ5OWM3Yzg0OWFiZGYyNTI0NzkzODhiMWFlNTM5MWFkZTllMWU5OGQxNTQ1YjdmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:26:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9EWTd1Vzgxc3JVT2N4WjBFTnBwcFE9PSIsInZhbHVlIjoiRlNpd2UrZm9sekQ1MmhUc1piMVllRnlSMGVWckhWZkZjSVRJc1hyRzZjejd1Q3B5TkVjN1ZmcFExY0t1WDhCZjY3aEJ0cGZsNFFmaHh3Ujl0dmxtMHgxVWZMM3RjVlE5ZENndkhkeWFTRzRHWFNVTDhmR0pFK1lvQ2xLR3NEdzMiLCJtYWMiOiI3MWVhMTRjZGFmYzc1NTJhN2M4YWI0NjBlN2VhNWY0NWIwYmE0OGViZjhkOTc0MThmMjE2ZWE0MjFiZmNkYTA2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:26:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImhyK2xxODhNUExoZUxtSXVkamNmd3c9PSIsInZhbHVlIjoiVmJUZ2xRZ3hlNlhNcVpjRktFTkFwZHQ1and3aEZ1eU1NY0YvRU82OHhmTURocGozMmVSU2FrVHoxTmxUSytIQmFwc3E3RDRhL1dCTlE0VFN4amZqMXdFR3ZyVW9MVnByS0Q5WGtUYXUxcllTM1FtM1JZcGFjZzBQaVp4cUZsMVEiLCJtYWMiOiIwYjI1MjI3Njk5ODgzYWY4ZmQ5OWM3Yzg0OWFiZGYyNTI0NzkzODhiMWFlNTM5MWFkZTllMWU5OGQxNTQ1YjdmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:26:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711121792\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-940487465 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=Xd80f94aac4c22c1e4bce5edd7b0b474a&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940487465\", {\"maxDepth\":0})</script>\n"}}