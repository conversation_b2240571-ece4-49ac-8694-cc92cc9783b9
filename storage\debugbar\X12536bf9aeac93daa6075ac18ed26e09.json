{"__meta": {"id": "X12536bf9aeac93daa6075ac18ed26e09", "datetime": "2025-07-29 15:18:44", "utime": **********.44095, "method": "GET", "uri": "/finance/proposal/edit/221", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 11, "messages": [{"message": "[15:18:42] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.571338, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2418", "message_html": null, "is_string": false, "label": "warning", "time": **********.287115, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2478", "message_html": null, "is_string": false, "label": "warning", "time": **********.287233, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3619", "message_html": null, "is_string": false, "label": "warning", "time": **********.287925, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.309136, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.30918, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 645", "message_html": null, "is_string": false, "label": "warning", "time": **********.309327, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3418", "message_html": null, "is_string": false, "label": "warning", "time": **********.32111, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.327222, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 637", "message_html": null, "is_string": false, "label": "warning", "time": **********.363948, "xdebug_link": null, "collector": "log"}, {"message": "[15:18:44] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.395233, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.171934, "end": **********.440978, "duration": 2.2690441608428955, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": **********.171934, "relative_start": 0, "end": **********.549429, "relative_end": **********.549429, "duration": 0.37749505043029785, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.549446, "relative_start": 0.*****************, "end": **********.440979, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 12, "templates": [{"name": "applications.admin.accounting.proposal.edit (\\resources\\views\\applications\\admin\\accounting\\proposal\\edit.blade.php)", "param_count": 14, "params": ["id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.accounting.proposal.proposal-edit (\\resources\\views\\livewire\\accounting\\proposal\\proposal-edit.blade.php)", "param_count": 36, "params": ["errors", "_instance", "proposalId", "customers", "billing_types", "categories", "templates", "items_list", "item_types", "proposal_type", "customer_id", "issue_date", "category_id", "proposal_template", "proposal_number", "items", "subtotal", "discount_total", "tax_total", "total", "loading", "saving", "error", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "id", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET finance/proposal/edit/{id}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\Admin\\Finance\\ProposalController@edit", "as": "finance.proposal.edit", "namespace": "App\\Http\\Controllers", "prefix": "/finance", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\Admin\\Finance\\ProposalController.php&line=23\">\\app\\Http\\Controllers\\Admin\\Finance\\ProposalController.php:23-26</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.027580000000000004, "accumulated_duration_str": "27.58ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 18, "namespace": null, "name": "\\app\\Services\\Finance\\FinanceProposalService.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.0036, "duration_str": "3.6ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 13.053}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 13.053, "width_percent": 2.719}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3048}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00529, "duration_str": "5.29ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3048", "connection": "osool_dev_db2", "start_percent": 15.772, "width_percent": 19.181}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7368 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00209, "duration_str": "2.09ms", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_dev_db2", "start_percent": 34.953, "width_percent": 7.578}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1929}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1929", "connection": "osool_dev_db2", "start_percent": 42.531, "width_percent": 5.511}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_dev_db2", "start_percent": 48.042, "width_percent": 3.988}, {"sql": "select * from `work_orders` where `project_user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:21", "connection": "osool_dev_db2", "start_percent": 52.03, "width_percent": 5.402}, {"sql": "select count(*) as aggregate from `service_provider_missing_items_requests` where `status` = 'requested' and 0 = 1", "type": "query", "params": [], "bindings": ["requested"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 26}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\RequestedItemService.php:26", "connection": "osool_dev_db2", "start_percent": 57.433, "width_percent": 1.668}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00458, "duration_str": "4.58ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_dev_db2", "start_percent": 59.101, "width_percent": 16.606}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 75.707, "width_percent": 3.372}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 897}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 79.079, "width_percent": 1.45}, {"sql": "select `id` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00395, "duration_str": "3.95ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_dev_db2", "start_percent": 80.529, "width_percent": 14.322}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 182}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 94.851, "width_percent": 3.662}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 758}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 182}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Models\\User.php:758", "connection": "osool_dev_db2", "start_percent": 98.513, "width_percent": 1.487}]}, "models": {"data": {"App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 1, "App\\Models\\ProjectsDetails": 2, "App\\Models\\User": 2}, "count": 6}, "livewire": {"data": {"accounting.proposal.proposal-edit #Wg27LWjAfkX83XUX2N5m": "array:5 [\n  \"data\" => array:21 [\n    \"proposalId\" => \"221\"\n    \"customers\" => array:57 [\n      0 => array:2 [\n        \"name\" => \"Test deal\"\n        \"id\" => 219\n      ]\n      1 => array:2 [\n        \"name\" => \"Test Client\"\n        \"id\" => 318\n      ]\n      2 => array:2 [\n        \"name\" => \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n        \"id\" => 323\n      ]\n      3 => array:2 [\n        \"name\" => \"Customer 1\"\n        \"id\" => 492\n      ]\n      4 => array:2 [\n        \"name\" => \"Customer 2\"\n        \"id\" => 493\n      ]\n      5 => array:2 [\n        \"name\" => \"Customer 3\"\n        \"id\" => 494\n      ]\n      6 => array:2 [\n        \"name\" => \"Customer 4\"\n        \"id\" => 495\n      ]\n      7 => array:2 [\n        \"name\" => \"Customer 5\"\n        \"id\" => 496\n      ]\n      8 => array:2 [\n        \"name\" => \"Customer 6\"\n        \"id\" => 497\n      ]\n      9 => array:2 [\n        \"name\" => \"Customer 7\"\n        \"id\" => 498\n      ]\n      10 => array:2 [\n        \"name\" => \"Customer 8\"\n        \"id\" => 499\n      ]\n      11 => array:2 [\n        \"name\" => \"Customer 9\"\n        \"id\" => 500\n      ]\n      12 => array:2 [\n        \"name\" => \"Customer 10\"\n        \"id\" => 501\n      ]\n      13 => array:2 [\n        \"name\" => \"Customer 11\"\n        \"id\" => 502\n      ]\n      14 => array:2 [\n        \"name\" => \"Customer 12\"\n        \"id\" => 503\n      ]\n      15 => array:2 [\n        \"name\" => \"Customer 13\"\n        \"id\" => 504\n      ]\n      16 => array:2 [\n        \"name\" => \"Customer 14\"\n        \"id\" => 505\n      ]\n      17 => array:2 [\n        \"name\" => \"Customer 15\"\n        \"id\" => 506\n      ]\n      18 => array:2 [\n        \"name\" => \"Customer 16\"\n        \"id\" => 507\n      ]\n      19 => array:2 [\n        \"name\" => \"Customer 17\"\n        \"id\" => 508\n      ]\n      20 => array:2 [\n        \"name\" => \"Customer 18\"\n        \"id\" => 509\n      ]\n      21 => array:2 [\n        \"name\" => \"Customer 19\"\n        \"id\" => 510\n      ]\n      22 => array:2 [\n        \"name\" => \"Customer 20\"\n        \"id\" => 511\n      ]\n      23 => array:2 [\n        \"name\" => \"Customer 21\"\n        \"id\" => 512\n      ]\n      24 => array:2 [\n        \"name\" => \"Customer 22\"\n        \"id\" => 513\n      ]\n      25 => array:2 [\n        \"name\" => \"Customer 23\"\n        \"id\" => 514\n      ]\n      26 => array:2 [\n        \"name\" => \"Customer 24\"\n        \"id\" => 515\n      ]\n      27 => array:2 [\n        \"name\" => \"Customer 25\"\n        \"id\" => 516\n      ]\n      28 => array:2 [\n        \"name\" => \"Customer 26\"\n        \"id\" => 517\n      ]\n      29 => array:2 [\n        \"name\" => \"Customer 27\"\n        \"id\" => 518\n      ]\n      30 => array:2 [\n        \"name\" => \"Customer 28\"\n        \"id\" => 519\n      ]\n      31 => array:2 [\n        \"name\" => \"Customer 29\"\n        \"id\" => 520\n      ]\n      32 => array:2 [\n        \"name\" => \"Customer 30\"\n        \"id\" => 521\n      ]\n      33 => array:2 [\n        \"name\" => \"Customer 31\"\n        \"id\" => 522\n      ]\n      34 => array:2 [\n        \"name\" => \"Customer 32\"\n        \"id\" => 523\n      ]\n      35 => array:2 [\n        \"name\" => \"Customer 33\"\n        \"id\" => 524\n      ]\n      36 => array:2 [\n        \"name\" => \"Customer 34\"\n        \"id\" => 525\n      ]\n      37 => array:2 [\n        \"name\" => \"Customer 35\"\n        \"id\" => 526\n      ]\n      38 => array:2 [\n        \"name\" => \"Customer 36\"\n        \"id\" => 527\n      ]\n      39 => array:2 [\n        \"name\" => \"Customer 37\"\n        \"id\" => 528\n      ]\n      40 => array:2 [\n        \"name\" => \"Customer 38\"\n        \"id\" => 529\n      ]\n      41 => array:2 [\n        \"name\" => \"Customer 39\"\n        \"id\" => 530\n      ]\n      42 => array:2 [\n        \"name\" => \"Customer 40\"\n        \"id\" => 531\n      ]\n      43 => array:2 [\n        \"name\" => \"Customer 41\"\n        \"id\" => 532\n      ]\n      44 => array:2 [\n        \"name\" => \"Customer 42\"\n        \"id\" => 533\n      ]\n      45 => array:2 [\n        \"name\" => \"Customer 43\"\n        \"id\" => 534\n      ]\n      46 => array:2 [\n        \"name\" => \"Customer 44\"\n        \"id\" => 535\n      ]\n      47 => array:2 [\n        \"name\" => \"Customer 45\"\n        \"id\" => 536\n      ]\n      48 => array:2 [\n        \"name\" => \"Customer 46\"\n        \"id\" => 537\n      ]\n      49 => array:2 [\n        \"name\" => \"Customer 47\"\n        \"id\" => 538\n      ]\n      50 => array:2 [\n        \"name\" => \"Customer 48\"\n        \"id\" => 539\n      ]\n      51 => array:2 [\n        \"name\" => \"Customer 49\"\n        \"id\" => 540\n      ]\n      52 => array:2 [\n        \"name\" => \"Customer 50\"\n        \"id\" => 541\n      ]\n      53 => array:2 [\n        \"name\" => \"Khansaa 18.06\"\n        \"id\" => 563\n      ]\n      54 => array:2 [\n        \"name\" => \"Fouzan\"\n        \"id\" => 564\n      ]\n      55 => array:2 [\n        \"name\" => \"Testing\"\n        \"id\" => 631\n      ]\n      56 => array:2 [\n        \"name\" => \"New Tenant Dev Server\"\n        \"id\" => 652\n      ]\n    ]\n    \"billing_types\" => array:3 [\n      \"product\" => \"Item Wise\"\n      \"project\" => \"Project Wise\"\n      \"parts\" => \"Parts Wise\"\n    ]\n    \"categories\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Test cat\"\n        \"id\" => 18\n      ]\n    ]\n    \"templates\" => array:10 [\n      \"template1\" => \"New York\"\n      \"template2\" => \"Toronto\"\n      \"template3\" => \"Rio\"\n      \"template4\" => \"London\"\n      \"template5\" => \"Istanbul\"\n      \"template6\" => \"Mumbai\"\n      \"template7\" => \"Hong Kong\"\n      \"template8\" => \"Tokyo\"\n      \"template9\" => \"Sydney\"\n      \"template10\" => \"Paris\"\n    ]\n    \"items_list\" => array:4 [\n      0 => array:2 [\n        \"id\" => 11\n        \"name\" => \"Test\"\n      ]\n      1 => array:2 [\n        \"id\" => 12\n        \"name\" => \"test 3\"\n      ]\n      2 => array:2 [\n        \"id\" => 18\n        \"name\" => \"bbb\"\n      ]\n      3 => array:2 [\n        \"id\" => 22\n        \"name\" => \"Service - Programing new feature\"\n      ]\n    ]\n    \"item_types\" => array:3 [\n      \"product\" => \"Products\"\n      \"service\" => \"Services\"\n      \"parts\" => \"Parts\"\n    ]\n    \"proposal_type\" => \"Accounting\"\n    \"customer_id\" => 564\n    \"issue_date\" => \"2025-07-29\"\n    \"category_id\" => 18\n    \"proposal_template\" => \"template1\"\n    \"proposal_number\" => \"#PROP000004\"\n    \"items\" => array:1 [\n      0 => array:8 [\n        \"id\" => 301\n        \"product_type\" => \"\"\n        \"item\" => 11\n        \"quantity\" => 1\n        \"price\" => 100\n        \"tax\" => \"0\"\n        \"discount\" => 25\n        \"description\" => \"Test1\"\n      ]\n    ]\n    \"subtotal\" => 100\n    \"discount_total\" => 25\n    \"tax_total\" => 0\n    \"total\" => 75\n    \"loading\" => false\n    \"saving\" => false\n    \"error\" => null\n  ]\n  \"name\" => \"accounting.proposal.proposal-edit\"\n  \"view\" => \"livewire.accounting.proposal.proposal-edit\"\n  \"component\" => \"App\\Http\\Livewire\\Accounting\\Proposal\\ProposalEdit\"\n  \"id\" => \"Wg27LWjAfkX83XUX2N5m\"\n]", "notifications.messages-notifications-list #xXmsUVIewOSdFMm58kfU": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"xXmsUVIewOSdFMm58kfU\"\n]", "notifications.new-notifications-list-top-nav #bpm7j462SHW0aIpCCOVP": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"bpm7j462SHW0aIpCCOVP\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#4071\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-29 15:16:43\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzkxNDAzLCJleHAiOjE3NTM3OTUwMDMsIm5iZiI6MTc1Mzc5MTQwMywianRpIjoiaWtMZk8yZmQzNE5INzBtYyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.k9rjfX9LEkwJrvqjTQbWiGrstrK42_H1lyVZHyQW1h4\"\n      ]\n      #original: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-29 15:16:43\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzNzkxNDAzLCJleHAiOjE3NTM3OTUwMDMsIm5iZiI6MTc1Mzc5MTQwMywianRpIjoiaWtMZk8yZmQzNE5INzBtYyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.k9rjfX9LEkwJrvqjTQbWiGrstrK42_H1lyVZHyQW1h4\"\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:2 [\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#4105\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4118\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #original: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:62 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n        54 => \"sleep_mode\"\n        55 => \"offline_mode\"\n        56 => \"attendance_mandatory\"\n        57 => \"admin_level\"\n        58 => \"role\"\n        59 => \"attendance_target\"\n        60 => \"salary\"\n        61 => \"show_extra_info\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 7368\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4177\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => \"#000\"\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => null\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/finance/proposal/edit/221\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "en", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/finance/proposal/edit/221", "status_code": "<pre class=sf-dump id=sf-dump-1426715547 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1426715547\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2039363151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039363151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1896564100 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1896564100\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1892437665 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IlMwekVxdVphVytoVnVsR29LMFluNnc9PSIsInZhbHVlIjoiUS9WL1d2elB5QzVxbkRPUFBaa1cwQT09IiwibWFjIjoiMDJjZDZiMzY3MDQyODlmODZiMzdlMDBkNTY1YTQ5MTc4Yjc5NDgzMDBlY2ViMmQzNGE4ZDBmZjc1OTBiNGVhNyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlhoQmNRTDFLSDBIMTAzODZiODA1dlE9PSIsInZhbHVlIjoidUR5bVU1TTNBamhDZDJGRVVldVNvV084c1hLS1J0NmFOOTdDTjdkZmVaMzluOE9wUFRkQUJDYWdRckhuYjZJYzM3Ym9OQWUvZlF5elFZeE04Vms2U1ZaUUtScG82M1BMWngvK2kwWTIwTVcyUnlyOXVQSUNFbTVtZG1WZnkxcGMiLCJtYWMiOiIwYTU1ODgwNTc0YjgwYmEyNGIyZDhiN2RjZDkyODdjOWEzODY1Y2Y4ZWYxMzEyMzUwMWNiMWM4OWQ5NGE0MTY5IiwidGFnIjoiIn0%3D; osool_session=upF0PyWpxWUKgHNh8wlwn7iqznYeNJSAi2JGSIsH</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892437665\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-791078879 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"245 characters\">http://osool-b2g.test/finance/customers/view/eyJpdiI6IlMwekVxdVphVytoVnVsR29LMFluNnc9PSIsInZhbHVlIjoiUS9WL1d2elB5QzVxbkRPUFBaa1cwQT09IiwibWFjIjoiMDJjZDZiMzY3MDQyODlmODZiMzdlMDBkNTY1YTQ5MTc4Yjc5NDgzMDBlY2ViMmQzNGE4ZDBmZjc1OTBiNGVhNyIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IlhoQmNRTDFLSDBIMTAzODZiODA1dlE9PSIsInZhbHVlIjoidUR5bVU1TTNBamhDZDJGRVVldVNvV084c1hLS1J0NmFOOTdDTjdkZmVaMzluOE9wUFRkQUJDYWdRckhuYjZJYzM3Ym9OQWUvZlF5elFZeE04Vms2U1ZaUUtScG82M1BMWngvK2kwWTIwTVcyUnlyOXVQSUNFbTVtZG1WZnkxcGMiLCJtYWMiOiIwYTU1ODgwNTc0YjgwYmEyNGIyZDhiN2RjZDkyODdjOWEzODY1Y2Y4ZWYxMzEyMzUwMWNiMWM4OWQ5NGE0MTY5IiwidGFnIjoiIn0%3D; osool_session=upF0PyWpxWUKgHNh8wlwn7iqznYeNJSAi2JGSIsH</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54143</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/finance/proposal/edit/221</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/finance/proposal/edit/221</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.1719</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791078879\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1248631468 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248631468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2075941809 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 12:18:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijh1bVo1WjVobXZZbnlmOUJvZDI0alE9PSIsInZhbHVlIjoiT1dDTGFxNE41a3NWVlh5TUN1eVpNN0dwQmNCQjFmVzlUaXUySG82RW5PNlJJbkpBOUhvVjM0a0ZYQTZIS0pzbFBVc1V5TUt0ZGhoMlJHejRHS1ZQN0NCaTVkV1NDZzFuQ0I5QVdOQ08wSDdlWDRrdlI2TmFTUkk3cnQzdEorTFoiLCJtYWMiOiI5MWI3MTQ5ZDU1MzBiNTk0Y2Y5YTU1MTQyNjNlZGVhYzAwNzJhNzMzNTJlMDE2YzkwMTQzMmIwNmRhZWI3YWIwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:18:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImNxVUQyQVM3L2NDNTJMZjg4YWU5V1E9PSIsInZhbHVlIjoidDNaMTd2Wm9MZ2grM3JlaE0velJDdkcraEoreHFDZThTSlo4UmNCUzJ2OFJpNkc4RW9hRmFNK3F0dnhOYjJ4bWhzUTAzSXdPOU9JOTNZNGpPR0xkbnNnUmNzTExtbW14V1ByOVB1VnBaZ1RlbmxXSUNEOXRwS2tqb0ExZGlHRUoiLCJtYWMiOiI5ODFmYzBhYTk4ZTY4ZjQ5ZDU5MWMwYTZkZjMzYzZhNDAxZWI5MmFlMTcyMGFjZGNiODAwOWJmNmU1YjYwYzdhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:18:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijh1bVo1WjVobXZZbnlmOUJvZDI0alE9PSIsInZhbHVlIjoiT1dDTGFxNE41a3NWVlh5TUN1eVpNN0dwQmNCQjFmVzlUaXUySG82RW5PNlJJbkpBOUhvVjM0a0ZYQTZIS0pzbFBVc1V5TUt0ZGhoMlJHejRHS1ZQN0NCaTVkV1NDZzFuQ0I5QVdOQ08wSDdlWDRrdlI2TmFTUkk3cnQzdEorTFoiLCJtYWMiOiI5MWI3MTQ5ZDU1MzBiNTk0Y2Y5YTU1MTQyNjNlZGVhYzAwNzJhNzMzNTJlMDE2YzkwMTQzMmIwNmRhZWI3YWIwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:18:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImNxVUQyQVM3L2NDNTJMZjg4YWU5V1E9PSIsInZhbHVlIjoidDNaMTd2Wm9MZ2grM3JlaE0velJDdkcraEoreHFDZThTSlo4UmNCUzJ2OFJpNkc4RW9hRmFNK3F0dnhOYjJ4bWhzUTAzSXdPOU9JOTNZNGpPR0xkbnNnUmNzTExtbW14V1ByOVB1VnBaZ1RlbmxXSUNEOXRwS2tqb0ExZGlHRUoiLCJtYWMiOiI5ODFmYzBhYTk4ZTY4ZjQ5ZDU5MWMwYTZkZjMzYzZhNDAxZWI5MmFlMTcyMGFjZGNiODAwOWJmNmU1YjYwYzdhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 14:18:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075941809\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-864945312 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">u7orpXhbbn3E9YeRIoyYAuI5HiEAgX3vXcJF4lLY</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://osool-b2g.test/finance/proposal/edit/221</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864945312\", {\"maxDepth\":0})</script>\n"}}