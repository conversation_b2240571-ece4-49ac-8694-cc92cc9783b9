<?php

namespace App\Http\Livewire\Accounting\Customers;

use App\Http\Traits\FunctionsTrait;
use Livewire\Component;
use Livewire\WithPagination;
use App\Services\Finance\FinanceProposalService;
use Illuminate\Support\Facades\Log;

class CustomerProposal extends Component
{
    use WithPagination, FunctionsTrait;

    public $customerId;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'issue_date';
    public $sortDirection = 'desc';
    public $showFullView = true;

    // API response data
    public $proposals = [];
    public $total = 0;
    public $currentPage = 1;
    public $lastPage = 1;
    public $loading = false;
    public $error = null;

    // Modal data
    public $editId = null;
    public $deleteId = null;
    public $deleteTitle = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'currentPage' => ['except' => 1, 'as' => 'page'],
    ];

    protected $listeners = [
        'delete' => 'delete',
        'refreshProposals' => 'fetchProposals',
    ];

    public function mount($customerId, $showFullView = true)
    {
        $this->customerId = $customerId;
        $this->showFullView = $showFullView;
        $this->fetchProposals();
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->fetchProposals();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
        $this->fetchProposals();
    }

    public function fetchProposals()
    {
        try {
            $this->loading = true;
            $this->error = null;

            $financeProposalService = app(FinanceProposalService::class);

            // Prepare API parameters
            $params = [
                'customer_id' => $this->customerId,
                'page' => $this->currentPage,
                'per_page' => $this->perPage,
                'search' => $this->search,
                'sort_by' => $this->sortField,
                'sort_order' => $this->sortDirection,
            ];

            // Remove empty parameters
            $params = array_filter($params, function($value) {
                return $value !== null && $value !== '';
            });

            $response = $financeProposalService->list($params);

            if (isset($response['status']) && $response['status'] === 'success') {
                $data = $response['data'] ?? [];
                $this->proposals = $data['items'] ?? [];
                $this->total = $data['total'] ?? 0;
                $this->currentPage = $data['current_page'] ?? 1;
                $this->lastPage = $data['last_page'] ?? 1;
            } else {
                // If API endpoint is not available yet, provide sample data for development
                if (app()->environment('local') && str_contains($response['message'] ?? '', 'Not Found')) {
                    $this->loadSampleData();
                } else {
                    $this->error = $response['message'] ?? __('accounting.errors.failed_to_fetch_proposals');
                    $this->proposals = [];
                    $this->total = 0;
                }
            }

        } catch (\Exception $e) {
            Log::error('Error fetching customer proposals: ' . $e->getMessage());

            // If in development and API is not available, show sample data
            if (app()->environment('local') && (str_contains($e->getMessage(), 'cURL error') || str_contains($e->getMessage(), 'Connection refused'))) {
                $this->loadSampleData();
            } else {
                $this->error = __('accounting.errors.something_went_wrong');
                $this->proposals = [];
                $this->total = 0;
            }
        } finally {
            $this->loading = false;
        }
    }

    private function loadSampleData()
    {
        // Sample data for development when API is not available
        $sampleProposals = [
            [
                'id' => 1,
                'proposal_number' => 'PS-2024001',
                'number' => 'PS-2024001',
                'issue_date' => '2024-01-15',
                'amount' => 25000.00,
                'status' => 'draft',
            ],
            [
                'id' => 2,
                'proposal_number' => 'PS-2024002',
                'number' => 'PS-2024002',
                'issue_date' => '2024-01-20',
                'amount' => 15000.00,
                'status' => 'sent',
            ],
            [
                'id' => 3,
                'proposal_number' => 'PS-2024003',
                'number' => 'PS-2024003',
                'issue_date' => '2024-01-25',
                'amount' => 35000.00,
                'status' => 'approved',
            ],
        ];

        // Filter by search if provided
        if (!empty($this->search)) {
            $sampleProposals = array_filter($sampleProposals, function($proposal) {
                return str_contains(strtolower($proposal['proposal_number']), strtolower($this->search)) ||
                       str_contains(strtolower($proposal['status']), strtolower($this->search));
            });
        }

        $this->proposals = array_slice($sampleProposals, ($this->currentPage - 1) * $this->perPage, $this->perPage);
        $this->total = count($sampleProposals);
        $this->lastPage = ceil($this->total / $this->perPage);
    }

    public function goToPage($page)
    {
        $this->currentPage = $page;
        $this->fetchProposals();
    }

    public function openEditModal($id)
    {
        $this->editId = $id;
        $this->emit('openEditProposalModal', $id);
    }

    public function openDeleteModal($id, $proposalNumber)
    {
        $this->deleteId = $id;
        $this->deleteTitle = $proposalNumber;
        $this->emit('confirmDelete', $id, $proposalNumber, 'delete');
    }

    public function convertToInvoice($id)
    {
        try {
            $financeProposalService = app(FinanceProposalService::class);
            $response = $financeProposalService->convertToInvoice($id);

            if (isset($response['status']) && $response['status'] === 'success') {
                session()->flash('success', __('accounting.proposals.converted_to_invoice_successfully'));
                $this->fetchProposals();
                $this->emit('refreshCustomerData');
            } else {
                session()->flash('error', $response['message'] ?? __('accounting.errors.failed_to_convert_proposal'));
            }
        } catch (\Exception $e) {
            Log::error('Error converting proposal to invoice: ' . $e->getMessage());
            session()->flash('error', __('accounting.errors.something_went_wrong'));
        }
    }

    public function delete($id)
    {
        try {
            $this->loading = true;
            $this->error = null;

            $financeProposalService = app(FinanceProposalService::class);
            $response = $financeProposalService->delete($id);

            if (isset($response['status']) && $response['status'] === 'success') {
                // Show success message using toastr pattern
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'success',
                    'message' => __('accounting.proposals.deleted_successfully')
                ]);

                $this->fetchProposals();
                $this->emit('refreshCustomerData');
            } else {
                $this->error = $response['message'] ?? __('accounting.errors.failed_to_delete_proposal');
                $this->dispatchBrowserEvent('show-toastr', [
                    'type' => 'error',
                    'message' => $this->error
                ]);
            }
        } catch (\Exception $e) {
            $this->error = __('accounting.errors.something_went_wrong');
            $this->dispatchBrowserEvent('show-toastr', [
                'type' => 'error',
                'message' => $this->error
            ]);
        } finally {
            $this->loading = false;
        }
    }

    public function render()
    {
        return view('livewire.accounting.customers.include.proposal');
    }
}
